
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Copy } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";

interface ImprovedTextSectionProps {
  title: string;
  content: string;
}

const ImprovedTextSection = ({ title, content }: ImprovedTextSectionProps) => {
  const [editableContent, setEditableContent] = useState(content);

  const handleCopy = () => {
    navigator.clipboard.writeText(editableContent);
    toast.success("Đã sao chép vào clipboard!");
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-lg">{title}</CardTitle>
        <Button
          variant="outline"
          size="sm"
          className="gap-1"
          onClick={handleCopy}
        >
          <Copy className="h-4 w-4" />
          <span>Sao chép</span>
        </Button>
      </CardHeader>
      <CardContent>
        <Textarea
          value={editableContent}
          onChange={(e) => setEditableContent(e.target.value)}
          className="min-h-[120px] text-sm"
          placeholder="Nội dung cải thiện sẽ hiển thị ở đây..."
        />
      </CardContent>
    </Card>
  );
};

export default ImprovedTextSection;
