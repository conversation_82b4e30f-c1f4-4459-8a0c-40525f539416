package com.resumeai.service;
// GeminiClient - Helper gọi Gemini API dùng chung
import com.google.genai.Client;
import com.google.genai.ResponseStream;
import com.google.genai.types.*;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.resumeai.dto.ResumeAnalysisDTO;
import com.resumeai.dto.SectionAnalysisDTO;
import com.resumeai.dto.FlexibleResumeAnalysisDTO;
import com.resumeai.dto.FlexibleSectionAnalysisDTO;

import autovalue.shaded.org.checkerframework.checker.nullness.qual.Nullable;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class GeminiClient {
    @Value("${gemini.api.key}")
    private String apiKey;

    @Value("${gemini.api.model}")
    private String modelName;

    private final Gson gson = new Gson();

    public GenerateContentResponse generateContent(List<Content> contents, Schema schema) {
        try (Client geminiClient = Client.builder().apiKey(apiKey).build()) {
            GenerateContentConfig.Builder configBuilder = GenerateContentConfig.builder();
            
            // Chỉ set schema nếu không null
            if (schema != null) {
                configBuilder.responseMimeType("application/json")
                           .responseSchema(schema);
            }
            
            GenerateContentConfig config = configBuilder.build();
            return geminiClient.models.generateContent(modelName, contents, config);
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi gọi API Gemini: " + e.getMessage(), e);
        }
    }

    public ResumeAnalysisDTO analyzeResume(String rawResumeText, @Nullable String jobDescription) {
        try {
            // Tạo prompt chi tiết cho Gemini
            String prompt;
            
       if (jobDescription != null && !jobDescription.trim().isEmpty()) {
            prompt = String.format("""
                Bạn là một chuyên gia tuyển dụng và hướng nghiệp với nhiều năm kinh nghiệm.

                Đây là một đoạn văn bản hồ sơ xin việc chưa được định dạng:
                ---
                %s
                ---

                Đây là mô tả công việc tương ứng:
                ---
                %s
                ---

                Hãy phân tích văn bản hồ sơ thô và thực hiện các nhiệm vụ sau:

                1. **Phát hiện ngôn ngữ**: Xác định ngôn ngữ chính của CV (Tiếng Việt, English, etc.)
                2. **Trích xuất và phân loại nội dung** từ văn bản thô thành các phần dựa trên cấu trúc thực tế của CV
                   - Tự động nhận diện các phần có trong CV (có thể là: Kinh nghiệm làm việc, Học vấn, Kỹ năng, Dự án, Chứng chỉ, Hoạt động, v.v.)
                   - Không bắt buộc phải có đủ 3 phần cố định
                3. **Phân tích từng phần** dựa trên mô tả công việc để tìm:
                   - Lỗi chính tả và ngữ pháp
                   - Vấn đề về độ rõ ràng và cách diễn đạt
                   - Nội dung mơ hồ hoặc thiếu tác động
                   - Kỹ năng thiếu liên quan đến công việc mục tiêu
                4. **Tạo phiên bản cải thiện** cho từng phần với:
                   - Sử dụng CÙNG NGÔN NGỮ với CV gốc
                   - Sửa lỗi chính tả và ngữ pháp
                   - Cải thiện độ rõ ràng và tác động
                   - Thêm từ khóa và kỹ năng phù hợp với JD
                   - Định dạng chuyên nghiệp

                **QUAN TRỌNG**:
                - Phải sử dụng cùng ngôn ngữ với CV đầu vào
                - Chỉ phân tích các phần thực sự có trong CV
                - Tên phần phải giữ nguyên như trong CV gốc

                Với mỗi phần được tìm thấy, hãy:
                - **noi_dung**: Trích xuất nội dung gốc từ văn bản thô (giữ nguyên văn bản gốc)
                - **de_xuat**: Viết lại toàn bộ phần đó với nội dung đã được cải thiện, sửa lỗi, và tối ưu hóa (CÙNG NGÔN NGỮ)
                - **ly_do**: Giải thích những thay đổi đã thực hiện và lý do (CÙNG NGÔN NGỮ)

                Trả kết quả theo định dạng JSON linh hoạt. Ví dụ:
                {
                  "section_1": {
                    "ten_phan": "Tên phần như trong CV gốc",
                    "noi_dung": "...",
                    "de_xuat": "...",
                    "ly_do": "..."
                  },
                  "section_2": {
                    "ten_phan": "Tên phần khác",
                    "noi_dung": "...",
                    "de_xuat": "...",
                    "ly_do": "..."
                  }
                }
                """, rawResumeText, jobDescription);
        } else {
            // Logic cho trường hợp không có job description
            prompt = String.format("""
                Bạn là một chuyên gia tuyển dụng và hướng nghiệp với nhiều năm kinh nghiệm.

                Đây là một đoạn văn bản hồ sơ xin việc chưa được định dạng:
                ---
                %s
                ---

                Hãy phân tích văn bản hồ sơ thô và thực hiện các nhiệm vụ sau:

                1. **Phát hiện ngôn ngữ**: Xác định ngôn ngữ chính của CV (Tiếng Việt, English, etc.)
                2. **Trích xuất và phân loại nội dung** từ văn bản thô thành các phần dựa trên cấu trúc thực tế của CV
                   - Tự động nhận diện các phần có trong CV (có thể là: Kinh nghiệm làm việc, Học vấn, Kỹ năng, Dự án, Chứng chỉ, Hoạt động, v.v.)
                   - Không bắt buộc phải có đủ 3 phần cố định
                3. **Phân tích từng phần** để tìm:
                   - Lỗi chính tả và ngữ pháp
                   - Vấn đề về độ rõ ràng và cách diễn đạt
                   - Nội dung mơ hồ hoặc thiếu tác động
                   - Cơ hội cải thiện để làm nổi bật thành tích
                4. **Tạo phiên bản cải thiện** cho từng phần với:
                   - Sử dụng CÙNG NGÔN NGỮ với CV gốc
                   - Sửa lỗi chính tả và ngữ pháp
                   - Cải thiện độ rõ ràng và tác động
                   - Sử dụng từ ngữ mạnh mẽ và số liệu cụ thể
                   - Định dạng chuyên nghiệp

                **QUAN TRỌNG**:
                - Phải sử dụng cùng ngôn ngữ với CV đầu vào
                - Chỉ phân tích các phần thực sự có trong CV
                - Tên phần phải giữ nguyên như trong CV gốc

                Với mỗi phần được tìm thấy, hãy:
                - **noi_dung**: Trích xuất nội dung gốc từ văn bản thô (giữ nguyên văn bản gốc)
                - **de_xuat**: Viết lại toàn bộ phần đó với nội dung đã được cải thiện, sửa lỗi, và tối ưu hóa (CÙNG NGÔN NGỮ)
                - **ly_do**: Giải thích những thay đổi đã thực hiện và lý do (CÙNG NGÔN NGỮ)

                Trả kết quả theo định dạng JSON linh hoạt. Ví dụ:
                {
                  "section_1": {
                    "ten_phan": "Tên phần như trong CV gốc",
                    "noi_dung": "...",
                    "de_xuat": "...",
                    "ly_do": "..."
                  },
                  "section_2": {
                    "ten_phan": "Tên phần khác",
                    "noi_dung": "...",
                    "de_xuat": "...",
                    "ly_do": "..."
                  }
                }
                """, rawResumeText);
        }
            

            // Tạo Content với builder pattern
            Content content = Content.builder()
                .role("user")
                .parts(List.of(Part.fromText(prompt)))
                .build();
            
            // Gọi API Gemini với schema null (để Gemini tự format JSON)
            GenerateContentResponse response = generateContent(List.of(content), null);
            
            // Trích xuất JSON từ response
            String responseText = response.text();
            if (responseText.contains("```json")) {
                responseText = responseText.replace("```json", "").trim();
            }
            if (responseText.contains("```")) {
                responseText = responseText.replace("```", "").trim();
            }

            ObjectMapper mapper = new ObjectMapper();

            // Làm sạch JSON response trước khi parse
            String cleanedResponse = cleanJsonResponse(responseText);

            System.out.println("=== DEBUG JSON PARSING ===");
            System.out.println("Original response length: " + responseText.length());
            System.out.println("Cleaned response length: " + cleanedResponse.length());
            System.out.println("First 200 chars of cleaned response: " +
                (cleanedResponse.length() > 200 ? cleanedResponse.substring(0, 200) + "..." : cleanedResponse));

            // Thử parse với format cũ trước
            try {
                ResumeAnalysisDTO dto = mapper.readValue(cleanedResponse, ResumeAnalysisDTO.class);
                System.out.println("Successfully parsed with old format");
                return dto;
            } catch (Exception e1) {
                System.out.println("Old format parsing failed: " + e1.getMessage());
                // Nếu không được, thử parse như Map để xử lý format linh hoạt
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> flexibleMap = mapper.readValue(cleanedResponse, Map.class);
                    System.out.println("Successfully parsed with flexible format");
                    return convertMapToOldFormat(flexibleMap);
                } catch (Exception e2) {
                    System.out.println("Error parsing with both formats: " + e1.getMessage() + " | " + e2.getMessage());
                    System.out.println("Response text: " + responseText);
                    System.out.println("Cleaned response: " + cleanedResponse);
                    throw new RuntimeException("Lỗi khi phân tích hồ sơ với Gemini: " + e2.getMessage(), e2);
                }
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw new RuntimeException("Lỗi khi phân tích hồ sơ với Gemini: " + e.getMessage(), e);
        }
    }

    /**
     * Clean JSON response by replacing smart quotes and other problematic characters
     */
    private String cleanJsonResponse(String jsonResponse) {
        if (jsonResponse == null) {
            return null;
        }

        String cleaned = jsonResponse
            // Replace smart quotes with regular quotes
            .replace("\u201C", "\"")  // Left double quotation mark
            .replace("\u201D", "\"")  // Right double quotation mark
            .replace("\u2018", "'")   // Left single quotation mark
            .replace("\u2019", "'")   // Right single quotation mark
            // Replace other problematic characters
            .replace("\u2013", "-")   // En dash
            .replace("\u2014", "-")   // Em dash
            .replace("\u2026", "...")  // Horizontal ellipsis
            // Remove any BOM characters
            .replace("\uFEFF", "")
            // Replace other common problematic characters
            .replace((char) 0x2018, '\'')
            .replace((char) 0x2019, '\'')
            .replace((char) 0x201C, '"')
            .replace((char) 0x201D, '"')
            .trim();

        // Tìm và trích xuất JSON object từ response
        int startIndex = cleaned.indexOf("{");
        int lastIndex = cleaned.lastIndexOf("}");

        if (startIndex != -1 && lastIndex != -1 && lastIndex > startIndex) {
            cleaned = cleaned.substring(startIndex, lastIndex + 1);
        }

        // Loại bỏ các ký tự control characters
        cleaned = cleaned.replaceAll("[\\x00-\\x1F\\x7F]", "");

        return cleaned;
    }

    /**
     * Convert FlexibleResumeAnalysisDTO to ResumeAnalysisDTO for backward compatibility
     */
    private ResumeAnalysisDTO convertToOldFormat(FlexibleResumeAnalysisDTO flexibleResult) {
        ResumeAnalysisDTO result = new ResumeAnalysisDTO();

        // Tìm các phần tương ứng trong flexible format
        flexibleResult.getSections().forEach((key, section) -> {
            String sectionName = section.getTenPhan().toLowerCase();

            if (sectionName.contains("kinh nghiệm") || sectionName.contains("experience")) {
                result.setKinhNghiemLamViec(new SectionAnalysisDTO(
                    section.getNoiDung(), section.getDeXuat(), section.getLyDo()));
            } else if (sectionName.contains("học vấn") || sectionName.contains("education")) {
                result.setHocVan(new SectionAnalysisDTO(
                    section.getNoiDung(), section.getDeXuat(), section.getLyDo()));
            } else if (sectionName.contains("kỹ năng") || sectionName.contains("skill")) {
                result.setKyNang(new SectionAnalysisDTO(
                    section.getNoiDung(), section.getDeXuat(), section.getLyDo()));
            }
        });

        return result;
    }

    /**
     * Convert Map to ResumeAnalysisDTO for flexible format handling
     */
    @SuppressWarnings("unchecked")
    private ResumeAnalysisDTO convertMapToOldFormat(Map<String, Object> flexibleMap) {
        ResumeAnalysisDTO result = new ResumeAnalysisDTO();

        // Tìm các phần trong flexible format
        flexibleMap.forEach((key, value) -> {
            if (value instanceof Map) {
                Map<String, Object> section = (Map<String, Object>) value;
                String tenPhan = (String) section.get("ten_phan");
                String noiDung = (String) section.get("noi_dung");
                String deXuat = (String) section.get("de_xuat");
                String lyDo = (String) section.get("ly_do");

                if (tenPhan != null) {
                    String sectionName = tenPhan.toLowerCase();
                    SectionAnalysisDTO sectionDTO = new SectionAnalysisDTO(noiDung, deXuat, lyDo);

                    if (sectionName.contains("kinh nghiệm") || sectionName.contains("experience")) {
                        result.setKinhNghiemLamViec(sectionDTO);
                    } else if (sectionName.contains("học vấn") || sectionName.contains("education")) {
                        result.setHocVan(sectionDTO);
                    } else if (sectionName.contains("kỹ năng") || sectionName.contains("skill")) {
                        result.setKyNang(sectionDTO);
                    }
                    // Nếu không khớp với 3 phần cố định, có thể thêm vào phần đầu tiên trống
                    else if (result.getKinhNghiemLamViec() == null) {
                        result.setKinhNghiemLamViec(sectionDTO);
                    } else if (result.getHocVan() == null) {
                        result.setHocVan(sectionDTO);
                    } else if (result.getKyNang() == null) {
                        result.setKyNang(sectionDTO);
                    }
                }
            }
        });

        return result;
    }

    // public ResumeAnalysisDTO analyzeResumeToDTO(String rawResumeText) {
    //     try {
    //         JsonObject jsonResult = analyzeResume(rawResumeText);
    //         return gson.fromJson(jsonResult, ResumeAnalysisDTO.class);
    //     } catch (Exception e) {
    //         throw new RuntimeException("Lỗi khi convert JsonObject sang DTO: " + e.getMessage(), e);
    //     }
    // }

    // DTOs cho kết quả phân tích
    public static class ResumeAnalysisResult {
        private SectionAnalysis kinh_nghiem_lam_viec;
        private SectionAnalysis hoc_van;
        private SectionAnalysis ky_nang;

        // Getters and setters
        public SectionAnalysis getKinh_nghiem_lam_viec() { return kinh_nghiem_lam_viec; }
        public void setKinh_nghiem_lam_viec(SectionAnalysis kinh_nghiem_lam_viec) { this.kinh_nghiem_lam_viec = kinh_nghiem_lam_viec; }
        
        public SectionAnalysis getHoc_van() { return hoc_van; }
        public void setHoc_van(SectionAnalysis hoc_van) { this.hoc_van = hoc_van; }
        
        public SectionAnalysis getKy_nang() { return ky_nang; }
        public void setKy_nang(SectionAnalysis ky_nang) { this.ky_nang = ky_nang; }
    }

    public static class SectionAnalysis {
        private String noi_dung;
        private String de_xuat;
        private String ly_do;

        // Getters and setters
        public String getNoi_dung() { return noi_dung; }
        public void setNoi_dung(String noi_dung) { this.noi_dung = noi_dung; }
        
        public String getDe_xuat() { return de_xuat; }
        public void setDe_xuat(String de_xuat) { this.de_xuat = de_xuat; }
        
        public String getLy_do() { return ly_do; }
        public void setLy_do(String ly_do) { this.ly_do = ly_do; }
    }
}