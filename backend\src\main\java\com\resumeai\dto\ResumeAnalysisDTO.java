package com.resumeai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO cho kết quả phân tích hồ sơ xin việc
 */
public class ResumeAnalysisDTO {
    
    @JsonProperty("kinh_nghiem_lam_viec")
    private SectionAnalysisDTO kinhNghiemLamViec;
    
    @JsonProperty("hoc_van")
    private SectionAnalysisDTO hocVan;
    
    @JsonProperty("ky_nang")
    private SectionAnalysisDTO kyNang;

    // Constructors
    public ResumeAnalysisDTO() {}

    public ResumeAnalysisDTO(
        SectionAnalysisDTO kinhNghiemLamViec, 
        SectionAnalysisDTO hocVan, 
        SectionAnalysisDTO kyNang) {
        this.kinhNghiemLamViec = kinhNghiemLamViec;
        this.hocVan = hocVan;
        this.kyNang = kyNang;
    }

    // Getters and Setters
    public SectionAnalysisDTO getKinhNghiemLamViec() {
        return kinhNghiemLamViec;
    }

    public void setKinhNghiemLamViec(SectionAnalysisDTO kinhNghiemLamViec) {
        this.kinhNghiemLamViec = kinhNghiemLamViec;
    }

    public SectionAnalysisDTO getHocVan() {
        return hocVan;
    }

    public void setHocVan(SectionAnalysisDTO hocVan) {
        this.hocVan = hocVan;
    }

    public SectionAnalysisDTO getKyNang() {
        return kyNang;
    }

    public void setKyNang(SectionAnalysisDTO kyNang) {
        this.kyNang = kyNang;
    }
}

