package com.resumeai.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashMap;
import java.util.Map;

/**
 * DTO linh hoạt cho kết quả phân tích hồ sơ xin việc
 * Hỗ trợ cấu trúc động dựa trên nội dung thực tế của CV
 */
public class FlexibleResumeAnalysisDTO {
    
    private Map<String, FlexibleSectionAnalysisDTO> sections = new HashMap<>();
    
    // Constructor
    public FlexibleResumeAnalysisDTO() {}
    
    @JsonAnySetter
    public void setSections(String key, FlexibleSectionAnalysisDTO value) {
        this.sections.put(key, value);
    }
    
    @JsonAnyGetter
    public Map<String, FlexibleSectionAnalysisDTO> getSections() {
        return sections;
    }
}


