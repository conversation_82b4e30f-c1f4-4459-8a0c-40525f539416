package com.resumeai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO cho từng phần phân tích linh hoạt
 */
public class FlexibleSectionAnalysisDTO {
    
    @JsonProperty("ten_phan")
    private String tenPhan;
    
    @JsonProperty("noi_dung")
    private String noiDung;
    
    @JsonProperty("de_xuat")
    private String deXuat;
    
    @JsonProperty("ly_do")
    private String lyDo;
    
    // Constructors
    public FlexibleSectionAnalysisDTO() {}
    
    public FlexibleSectionAnalysisDTO(String tenPhan, String noiDung, String deXuat, String lyDo) {
        this.tenPhan = tenPhan;
        this.noiDung = noiDung;
        this.deXuat = deXuat;
        this.lyDo = lyDo;
    }
    
    // Getters and Setters
    public String getTenPhan() {
        return tenPhan;
    }
    
    public void setTenPhan(String tenPhan) {
        this.tenPhan = tenPhan;
    }
    
    public String getNoiDung() {
        return noiDung;
    }
    
    public void setNoiDung(String noiDung) {
        this.noiDung = noiDung;
    }
    
    public String getDeXuat() {
        return deXuat;
    }
    
    public void setDeXuat(String deXuat) {
        this.deXuat = deXuat;
    }
    
    public String getLyDo() {
        return lyDo;
    }
    
    public void setLyDo(String lyDo) {
        this.lyDo = lyDo;
    }
}
