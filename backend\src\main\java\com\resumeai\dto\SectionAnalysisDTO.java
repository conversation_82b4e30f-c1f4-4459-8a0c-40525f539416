package com.resumeai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO cho từng phần phân tích (kinh nghiệm, họ<PERSON> vấn, k<PERSON> năng)
 */
public class SectionAnalysisDTO {

    @JsonProperty("noi_dung")
    private String noiDung;

    @JsonProperty("de_xuat")
    private String deXuat;

    @JsonProperty("ly_do")
    private String lyDo;

    // Constructors
    public SectionAnalysisDTO() {}

    public SectionAnalysisDTO(String noiDung, String deXuat, String lyDo) {
        this.noiDung = noiDung;
        this.deXuat = deXuat;
        this.lyDo = lyDo;
    }

    // Getters and Setters
    public String getNoiDung() {
        return noiDung;
    }

    public void setNoiDung(String noiDung) {
        this.noiDung = noiDung;
    }

    public String getDeXuat() {
        return deXuat;
    }

    public void setDeXuat(String deXuat) {
        this.deXuat = deXuat;
    }

    public String getLyDo() {
        return lyDo;
    }

    public void setLyDo(String lyDo) {
        this.lyDo = lyDo;
    }
}
